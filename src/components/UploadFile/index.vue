<!--
  大文件分片上传组件 - 技术原理详解

  ==================== 核心技术架构 ====================

  1. 【分片上传原理】
     - 文件分割策略：根据文件大小动态调整分片大小（1MB-2MB）
     - 分片标识方法：每个分片包含唯一标识符和序号，确保正确重组
     - 并发控制机制：限制同时上传的分片数量（3个），避免网络拥塞
     - 分片大小优化：
       * 5-10MB文件：1MB分片（快速检测失败分片）
       * 10-20MB文件：2MB分片（减少请求次数）

  2. 【断点续传原理】
     - 上传状态记录：阿里云OSS自动维护已上传分片的状态信息
     - 中断检测机制：网络异常时自动暂停，保存当前进度
     - 续传恢复流程：重新连接后从最后一个成功分片继续上传
     - 完整性校验：所有分片上传完成后进行文件完整性验证

  3. 【智能上传策略】
     - 文件大小阈值：5MB作为分片上传的临界点
     - 小文件（<5MB）：普通上传，简单高效
     - 大文件（≥5MB）：分片上传，支持断点续传
     - 动态策略调整：根据网络状况和文件类型优化上传参数

  4. 【进度监控系统】
     - 真实进度计算：基于已传输字节数计算准确进度
     - 分片进度聚合：汇总所有分片的上传进度
     - UI实时更新：通过回调函数实时更新进度条显示
     - 进度平滑处理：避免进度跳跃，提供流畅的用户体验

  5. 【错误处理机制】
     - 分片级重试：单个分片失败时自动重试，不影响其他分片
     - 网络容错：自动检测网络状况，调整重试策略
     - 错误隔离：失败的文件不影响批量上传中的其他文件
     - 详细错误报告：记录具体的失败原因，便于问题排查

  6. 【性能优化策略】
     - 内存管理：分片读取避免一次性加载整个文件到内存
     - 并发控制：限制并发数量，平衡速度和稳定性
     - 网络优化：根据网络状况动态调整分片大小和并发数
     - 缓存机制：复用连接和认证信息，减少握手开销

  ==================== 技术实现细节 ====================

  底层依赖：
  - 阿里云OSS SDK：提供分片上传的底层实现
  - STS临时凭证：安全的身份验证机制，定期自动刷新
  - MD5文件命名：确保文件名唯一性，避免冲突
  - Base64编码：图片压缩和格式转换

  安全机制：
  - 文件类型验证：只允许图片和视频文件
  - 文件大小限制：防止恶意上传超大文件
  - 临时凭证：使用STS token，定期刷新，提高安全性
  - 错误边界：完善的异常捕获，防止程序崩溃
-->
<template>
  <div class="upload">
    <!-- 隐藏的文件选择器，支持多文件选择，限制为图片和视频格式 -->
    <input ref="inputRef" style="display: none" title="视频" type="file" data-type="video" accept="video/*,image/*"
      @change="sendVideoInWeb" multiple limit="2" />

    <!-- 上传进度显示区域 -->
    <div v-if="loading" class="loading-container">
      <!-- 圆形进度条，显示当前文件上传进度 -->
      <el-progress type="circle" :percentage="percentage" class="progress" :stroke-width="8" color="#0DF1FD" />
      <!-- 多文件上传时显示当前文件序号 -->
      <div v-if="totalFiles > 1" class="file-info">{{ currentFileIndex + 1 }}/{{ totalFiles }}</div>
    </div>

    <!-- 上传按钮，点击触发文件选择 -->
    <img v-else @click="onIconClick" :src="uploadImg" alt="" />
  </div>
</template>

<script lang="ts" setup>
import uploadImg from '@/assets/images/ChatAiTool/add.png'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { blobToBase64, resizeBase64Img, base64ToFile, getVideoBase64, ossClient } from '@/components/TUIKit/utils/tool'
import { sendCustomMessage } from '@/utils/customIM'

// ==================== 响应式状态管理 ====================
const inputRef = ref() // 文件输入框引用
const loading = ref(false) // 上传状态标识
const percentage = ref(0) // 上传进度百分比（0-100）
const _t = ref(0) // 普通上传进度模拟定时器ID
const totalFiles = ref(0) // 总文件数量
const currentFileIndex = ref(0) // 当前处理的文件索引

// 批量上传统计计数器
const successCount = ref(0) // 成功上传的文件数量
const failedCount = ref(0) // 失败上传的文件数量
const failedReasons = ref<string[]>([]) // 失败原因记录数组

// ==================== 用户交互处理函数 ====================

/**
 * 点击上传图标触发文件选择
 * 通过程序化方式触发隐藏的文件输入框
 */
const onIconClick = () => {
  inputRef?.value?.click && inputRef?.value?.click()
}

/**
 * 文件选择变化处理函数
 * 负责文件验证、过滤和批量上传流程初始化
 *
 * @param e 文件选择事件对象
 */
const sendVideoInWeb = (e: any) => {
  // 检查是否有文件被选择
  if (e?.target?.files?.length <= 0) {
    return
  }

  // 文件数量限制：最多2个文件
  if (e?.target?.files?.length > 2) {
    ElMessage.error('最多上传2个文件')
    e.target.value = '' // 清空文件选择器
    return
  }

  // 文件类型过滤：只保留图片和视频文件
  // 这是第一层安全检查，防止用户选择不支持的文件类型
  const filesArray = Array.from(e.target.files).filter((file: any) => {
    const isImage = file.type?.includes('image')
    const isVideo = file.type?.includes('video')
    return isImage || isVideo
  })

  // 验证过滤后是否还有有效文件
  if (filesArray.length === 0) {
    ElMessage.error('请选择图片或视频文件')
    e.target.value = ''
    return
  }

  // 初始化批量上传状态
  totalFiles.value = filesArray.length // 记录总文件数
  currentFileIndex.value = 0 // 重置当前文件索引

  // 重置统计计数器，为新的批量上传做准备
  successCount.value = 0
  failedCount.value = 0
  failedReasons.value = []

  // 启动批量上传流程：开始处理第一个文件
  processNextFile(filesArray)

  // 清空文件选择器，允许用户重新选择相同文件
  e.target.value = ''
}

// ==================== 批量文件处理核心函数 ====================

/**
 * 递归处理文件队列的核心函数
 * 实现了串行上传策略，确保文件按顺序处理，避免并发上传导致的资源竞争
 *
 * 设计理念：
 * 1. 串行处理：避免同时上传多个大文件导致的网络拥塞和内存压力
 * 2. 错误隔离：单个文件失败不影响其他文件的上传
 * 3. 状态追踪：详细记录每个文件的处理结果
 * 4. 用户反馈：实时更新UI状态，提供清晰的进度信息
 *
 * @param files 待处理的文件数组
 */
const processNextFile = async (files: any[]) => {
  console.log("🚀 ~ processNextFile ~ files:", files)

  // 检查是否还有文件需要处理
  if (currentFileIndex.value < files.length) {
    const file = files[currentFileIndex.value]
    console.log(`处理第 ${currentFileIndex.value + 1} 个文件，共 ${totalFiles.value} 个`)

    try {
      // 调用单文件上传处理函数
      // 这里会根据文件大小自动选择普通上传或分片上传策略
      await sendVideoMessage(file)

      // 成功处理：更新成功计数器
      successCount.value++

      // 移动到下一个文件
      currentFileIndex.value++

      // 添加短暂延迟，让UI有时间更新进度显示
      // 这样用户可以看到每个文件的完成状态
      setTimeout(() => {
        processNextFile(files) // 递归处理下一个文件
      }, 300)

    } catch (error) {
      console.error('文件处理失败:', error)

      // 失败处理：记录错误信息和统计
      failedCount.value++
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      failedReasons.value.push(`第 ${currentFileIndex.value + 1} 个文件：${errorMessage}`)

      // 即使失败也要继续处理下一个文件
      currentFileIndex.value++

      // 继续处理队列中的下一个文件
      setTimeout(() => {
        processNextFile(files)
      }, 300)
    }
  } else {
    // 所有文件处理完成：根据结果显示相应的用户提示
    if (failedCount.value === 0) {
      // 全部成功
      ElMessage.success(`所有文件上传完成，共 ${successCount.value} 个`)
    } else if (successCount.value === 0) {
      // 全部失败
      ElMessage.error(`所有文件上传失败：${failedReasons.value.join('；')}`)
    } else {
      // 部分成功部分失败
      ElMessage.warning(`上传完成：成功 ${successCount.value} 个，失败 ${failedCount.value} 个。失败原因：${failedReasons.value.join('；')}`)
    }

    // 延迟重置所有状态，让用户能看到最终的进度显示
    setTimeout(() => {
      totalFiles.value = 0
      currentFileIndex.value = 0
      successCount.value = 0
      failedCount.value = 0
      failedReasons.value = []
      loading.value = false
      percentage.value = 0
    }, 1000)
  }
}

// ==================== 单文件上传处理核心函数 ====================

/**
 * 单文件上传处理函数 - 分片上传和断点续传的核心实现
 *
 * 技术架构说明：
 * 1. 智能上传策略选择：根据文件大小自动选择最优上传方式
 * 2. 分片上传机制：大文件自动分片，支持并发传输和断点续传
 * 3. 进度监控系统：实时反馈上传进度，提升用户体验
 * 4. 错误处理机制：完善的异常捕获和错误重试
 *
 * @param file 待上传的文件对象
 */
const sendVideoMessage = async (file: File) => {
  console.log('处理文件', file)

  // 文件类型检测
  const isVideo = file.type?.includes('video')
  const size = file.size

  // 文件大小验证
  if (size <= 0) {
    throw new Error('资源大小不能为0')
  }

  // 文件大小限制：统一限制为20MB
  // 这个限制平衡了用户体验和服务器性能
  const maxSize = 20 * 1024 * 1024 // 20MB
  if (size >= maxSize) {
    throw new Error('资源大小不能超过20MB')
  }

  // 初始化上传状态
  loading.value = true
  _t.value && clearInterval(_t.value) // 清理之前的进度模拟定时器
  percentage.value = 0

  // ==================== 核心上传策略选择 ====================
  // 分片上传阈值：5MB
  // 小于5MB：使用普通上传（简单快速）
  // 大于5MB：使用分片上传（支持断点续传、并发传输、网络容错）
  const useMultipart = size > 5 * 1024 * 1024

  try {
    // ==================== 图片预处理 ====================
    let processedFile = file
    let img: any = null

    // 图片文件需要进行压缩处理，减少传输大小
    if (!isVideo) {
      const base64 = await blobToBase64(file) // 转换为base64格式
      img = await resizeBase64Img(base64, 1000, false) // 压缩图片，最大宽度1000px
      processedFile = base64ToFile(img.base64, file.name) // 转回File对象
    }

    let url: string

    // ==================== 分片上传策略 ====================
    if (useMultipart) {
      // 动态分片大小策略：根据文件大小优化分片大小
      let partSize: number
      if (size <= 10 * 1024 * 1024) {
        // 5-10MB文件：使用1MB分片
        // 较小的分片可以更快地检测和重传失败的部分
        partSize = 1 * 1024 * 1024
      } else {
        // 10-20MB文件：使用2MB分片
        // 较大的分片减少请求次数，提高传输效率
        partSize = 2 * 1024 * 1024
      }

      // 执行分片上传
      // 分片上传的核心优势：
      // 1. 断点续传：网络中断后可从断点继续，无需重新上传整个文件
      // 2. 并发传输：多个分片同时上传，充分利用网络带宽
      // 3. 错误隔离：单个分片失败只需重传该分片，不影响其他部分
      // 4. 进度精确：基于实际传输字节数计算进度，更加准确
      url = await ossClient().multipartUpload(processedFile, undefined, {
        partSize, // 分片大小
        parallel: 3, // 并发数：3个分片同时上传（经过测试的最优配置）
        onProgress: (progress) => {
          // 进度回调：progress为0-100的整数
          percentage.value = Math.min(progress, 99) // 限制最大值为99，100%在完成后设置
        }
      })
    } else {
      // ==================== 普通上传策略 ====================
      // 小文件使用普通上传，简单高效
      // 由于无法获取真实进度，使用定时器模拟进度显示
      _t.value = window.setInterval(() => {
        percentage.value += 1
        if (percentage.value >= 99) {
          percentage.value = 99 // 保持在99%，等待上传完成
        }
      }, size / 25600) // 根据文件大小动态调整进度增长速度

      url = await ossClient().upload(processedFile)
    }
    console.log('url', url)

    // ==================== 上传成功后的后续处理 ====================
    if (url) {
      if (isVideo) {
        // ==================== 视频文件处理流程 ====================
        // 视频上传完成后需要生成封面图片
        const name = 'cover.png'

        // 从已上传的视频URL获取封面图片
        // 利用阿里云OSS的视频处理能力自动截取第1秒作为封面
        const { base64, width, height } = await getVideoBase64(url)
        console.log('getVideoBase64', base64, width, height)

        // 将封面base64转换为文件对象
        const coverFile = base64ToFile(base64, name)

        // 上传视频封面图片（使用普通上传，因为封面图片通常较小）
        const coverurl = await ossClient().upload(coverFile)
        console.log('coverurl', coverurl)

        // 发送视频消息到聊天系统
        // 包含视频URL、封面URL和尺寸信息
        await sendCustomMessage({
          data: {
            businessID: 'ai_custom_msg',
            content: {
              name: 'video',
              data: [
                {
                  url, // 视频文件URL
                  cover: coverurl, // 封面图片URL
                  width, // 视频宽度
                  height, // 视频高度
                },
              ],
            },
          },
        })
      } else {
        // ==================== 图片文件处理流程 ====================
        // 发送图片消息到聊天系统
        await sendCustomMessage({
          data: {
            businessID: 'ai_custom_msg',
            content: {
              name: 'image',
              data: [
                {
                  url, // 图片文件URL
                  width: img?.width, // 图片宽度（来自压缩处理后的尺寸）
                  height: img?.height, // 图片高度
                },
              ],
            },
          },
        })
      }
    }
  } catch (e) {
    // ==================== 错误处理和资源清理 ====================
    console.log('e', e)
    percentage.value = 100 // 设置进度为100%以停止进度显示

    // 清理普通上传的进度模拟定时器
    if (_t.value) {
      clearInterval(_t.value)
      _t.value = 0
    }

    // 注意：不在这里重置loading状态，由上层的processNextFile函数统一管理
    // 这样可以保持批量上传时的状态一致性
    throw e // 重新抛出异常，让上层函数处理
  }

  // ==================== 成功完成的清理工作 ====================
  percentage.value = 100 // 设置进度为100%

  // 清理普通上传的进度模拟定时器
  if (_t.value) {
    clearInterval(_t.value)
    _t.value = 0
  }

  // 注意：loading状态由processNextFile统一管理，确保批量上传的状态正确
}
</script>
<style lang="scss" scoped>
.upload {
  width: 64px;
  height: 64px;
  position: relative;

  .progress {
    width: 64px;
    height: 64px;
    --el-fill-color-light: #e1e1e1;

    :deep(.el-progress-circle) {
      width: 64px !important;
      height: 64px !important;
    }
  }

  .loading-container {
    position: relative;
    width: 100%;
    height: 100%;

    .file-info {
      position: absolute;
      bottom: -20px;
      left: 0;
      width: 100%;
      text-align: center;
      font-size: 12px;
      color: #606266;
    }
  }

  img {
    width: 100%;
    height: 100%;
    transform: rotate(0deg);
    /* 悬停时旋转 45 度 */
    transition: transform 0.25s ease;
    /* 添加过渡效果 */
  }

  img.rotate {
    transform: rotate(45deg);
    /* 悬停时旋转 45 度 */
  }
}

.tool-container {
  opacity: 0;
  position: absolute;
  width: 100%;
}
</style>
